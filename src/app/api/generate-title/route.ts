import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { detectContentType, ContentType, generateContentSummary } from '@/lib/content-detector'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

/**
 * 为文本内容生成简洁标题
 * @param content 文本内容
 * @returns 生成的标题
 */
async function generateTitleForText(content: string): Promise<string> {
  // 检查是否有有效的OpenAI API密钥
  const apiKey = process.env.OPENAI_API_KEY
  if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
    // 返回基于内容的简单标题
    const summary = generateContentSummary(content, 50)
    const words = summary.split(/\s+/).slice(0, 5).join(' ')
    return words.length > 7 ? words.substring(0, 7) : words
  }

  try {
    // 生成内容摘要用于标题生成
    const contentSummary = generateContentSummary(content, 300)
    
    const response = await openai.chat.completions.create({
      model: process.env.TITLE_GENERATION_MODEL || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的标题生成助手。请为用户提供的内容生成一个简洁、准确的标题。要求：1. 标题长度不超过7个字 2. 准确概括内容核心 3. 使用中文 4. 避免使用标点符号'
        },
        {
          role: 'user',
          content: `请为以下内容生成一个7个字以内的简洁标题：\n\n${contentSummary}`
        }
      ],
      max_tokens: parseInt(process.env.TITLE_MAX_TOKENS || '50'),
      temperature: parseFloat(process.env.TITLE_TEMPERATURE || '0.3')
    })

    const generatedTitle = response.choices[0]?.message?.content?.trim() || ''
    
    // 确保标题不超过7个字
    if (generatedTitle.length > 7) {
      return generatedTitle.substring(0, 7)
    }
    
    return generatedTitle || '新内容'
  } catch (error) {
    console.error('标题生成失败:', error)
    // 降级处理：使用内容前几个字作为标题
    const fallbackTitle = content.trim().substring(0, 7)
    return fallbackTitle || '新内容'
  }
}

/**
 * 从网页标题提取简洁标题
 * @param webTitle 网页原始标题
 * @returns 处理后的标题
 */
function processWebTitle(webTitle: string): string {
  // 移除常见的网站后缀
  const cleanTitle = webTitle
    .replace(/\s*[-–—|]\s*.*$/, '') // 移除 - | 后的内容
    .replace(/\s*_.*$/, '') // 移除 _ 后的内容
    .replace(/\s*\(.*\)$/, '') // 移除括号内容
    .replace(/\s*【.*】$/, '') // 移除中文括号内容
    .trim()

  // 如果标题过长，截取前7个字
  if (cleanTitle.length > 7) {
    return cleanTitle.substring(0, 7)
  }
  
  return cleanTitle || '网页内容'
}

export async function POST(request: NextRequest) {
  try {
    const { content, contentType, webTitle } = await request.json()
    
    if (!content) {
      return NextResponse.json({ error: '缺少内容参数' }, { status: 400 })
    }

    // 检测内容类型（如果未提供）
    const detectionResult = contentType ? 
      { type: contentType } : 
      detectContentType(content)

    let title: string

    if (detectionResult.type === ContentType.URL) {
      // 对于URL，使用网页标题或域名
      if (webTitle) {
        title = processWebTitle(webTitle)
      } else {
        // 如果没有网页标题，使用域名
        try {
          const url = new URL(content)
          const domain = url.hostname.replace('www.', '')
          title = domain.length > 7 ? domain.substring(0, 7) : domain
        } catch (error) {
          title = '网页链接'
        }
      }
    } else {
      // 对于文本内容，使用AI生成标题
      title = await generateTitleForText(content)
    }

    return NextResponse.json({
      title,
      contentType: detectionResult.type,
      success: true
    })

  } catch (error) {
    console.error('标题生成API错误:', error)
    return NextResponse.json(
      { error: '标题生成失败', success: false },
      { status: 500 }
    )
  }
}

// 支持GET请求用于测试
export async function GET() {
  return NextResponse.json({
    message: '标题生成API正常运行',
    supportedMethods: ['POST'],
    requiredParams: ['content'],
    optionalParams: ['contentType', 'webTitle']
  })
}
