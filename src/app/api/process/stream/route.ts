import { NextRequest } from 'next/server'
import { scrapeWebPage } from '@/lib/scraper'
import { streamKnowledgeCards } from '@/lib/ai'
import { detectContentType, ContentType } from '@/lib/content-detector'
import OpenAI from 'openai'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// 智能标题生成函数
async function generateSmartTitle(input: string, contentType: ContentType, webTitle?: string): Promise<string> {
  if (contentType === ContentType.URL) {
    // 对于URL，使用网页标题或域名
    if (webTitle) {
      // 处理网页标题
      const cleanTitle = webTitle
        .replace(/\s*[-–—|]\s*.*$/, '') // 移除 - | 后的内容
        .replace(/\s*_.*$/, '') // 移除 _ 后的内容
        .replace(/\s*\(.*\)$/, '') // 移除括号内容
        .replace(/\s*【.*】$/, '') // 移除中文括号内容
        .trim()

      return cleanTitle.length > 7 ? cleanTitle.substring(0, 7) : cleanTitle || '网页内容'
    } else {
      // 如果没有网页标题，使用域名
      try {
        const url = new URL(input)
        const domain = url.hostname.replace('www.', '')
        return domain.length > 7 ? domain.substring(0, 7) : domain
      } catch (error) {
        return '网页链接'
      }
    }
  } else {
    // 对于文本内容，使用AI生成标题
    return await generateTitleForText(input)
  }
}

// 为文本内容生成简洁标题
async function generateTitleForText(content: string): Promise<string> {
  // 检查是否有有效的OpenAI API密钥
  const apiKey = process.env.OPENAI_API_KEY
  if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
    // 返回基于内容的简单标题
    const words = content.trim().split(/\s+/).slice(0, 5).join(' ')
    return words.length > 7 ? words.substring(0, 7) : words || '新内容'
  }

  try {
    // 生成内容摘要用于标题生成
    const contentSummary = content.length > 300 ? content.substring(0, 300) + '...' : content

    const response = await openai.chat.completions.create({
      model: process.env.TITLE_GENERATION_MODEL || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的标题生成助手。请为用户提供的内容生成一个简洁、准确的标题。要求：1. 标题长度不超过7个字 2. 准确概括内容核心 3. 使用中文 4. 避免使用标点符号'
        },
        {
          role: 'user',
          content: `请为以下内容生成一个7个字以内的简洁标题：\n\n${contentSummary}`
        }
      ],
      max_tokens: parseInt(process.env.TITLE_MAX_TOKENS || '50'),
      temperature: parseFloat(process.env.TITLE_TEMPERATURE || '0.3')
    })

    const generatedTitle = response.choices[0]?.message?.content?.trim() || ''

    // 确保标题不超过7个字
    if (generatedTitle.length > 7) {
      return generatedTitle.substring(0, 7)
    }

    return generatedTitle || '新内容'
  } catch (error) {
    console.error('标题生成失败:', error)
    // 降级处理：使用内容前几个字作为标题
    const fallbackTitle = content.trim().substring(0, 7)
    return fallbackTitle || '新内容'
  }
}

export async function POST(request: NextRequest) {
  try {
    const { input, type } = await request.json()

    if (!input) {
      return new Response('Missing input (URL or text)', { status: 400 })
    }

    // 自动检测内容类型（如果未提供type参数）
    const detectionResult = type ?
      { type: type === 'url' ? ContentType.URL : ContentType.TEXT } :
      detectContentType(input)

    let contentToProcess: string
    let title: string
    let webTitle: string | undefined

    if (detectionResult.type === ContentType.URL || type === 'url') {
      const scrapedContent = await scrapeWebPage(input)
      contentToProcess = scrapedContent.textContent
      webTitle = scrapedContent.title

      // 使用智能标题生成
      title = await generateSmartTitle(input, ContentType.URL, webTitle)
    } else {
      contentToProcess = input

      // 使用智能标题生成
      title = await generateSmartTitle(input, ContentType.TEXT)
    }

    if (!contentToProcess) {
      return new Response('Failed to get content to process', { status: 500 })
    }
    
    // 获取来自OpenAI的流
    const aiStream = await streamKnowledgeCards(contentToProcess)

    // 创建一个新的可读流，用于将AI流转换为SSE格式
    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          // 发送基本信息作为第一个事件
          controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ type: 'basic_info', data: { title, content: contentToProcess } })}\n\n`))
          
          for await (const chunk of aiStream) {
            const content = chunk.choices[0]?.delta?.content || ''
            if (content) {
              // 将每个AI内容块作为SSE事件发送
              const payload = { type: 'ai_note', data: { content } }
              controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(payload)}\n\n`))
            }
          }
          // 发送完成信号，附带 isComplete 标记，便于前端最终处理
          const finalPayload = { type: 'ai_note', data: { content: '', isComplete: true } }
          controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(finalPayload)}\n\n`))
          controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
          controller.close()
        } catch (error) {
          console.error('Streaming error:', error)
          controller.error(error)
        }
      }
    })

    return new Response(readableStream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error) {
    console.error('Error in process/stream:', error)
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: 'Failed to process content', details: errorMessage }), { status: 500 })
  }
} 