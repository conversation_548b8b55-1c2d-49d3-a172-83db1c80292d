'use client'

import React, { useState, useRef, useEffect } from 'react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import ModernLoader from '@/components/ui/ModernLoader'
import { useAppStore, useActiveTab, useActiveChatMessages, useActiveStreamingNote } from '@/lib/store'
import { Send, Brain, Sparkles, ChevronDown, ChevronUp } from 'lucide-react'

const AIAssistant: React.FC = () => {
  const { addChatMessage, updateChatMessage } = useAppStore()
  const activeTab = useActiveTab()
  const chatMessages = useActiveChatMessages()
  const streamingNote = useActiveStreamingNote()
  const [chatInput, setChatInput] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const [isStructuredNotesExpanded, setIsStructuredNotesExpanded] = useState(true)
  const chatContainerRef = useRef<HTMLDivElement>(null)

  // 判断是否有聊天记录
  const hasChatMessages = chatMessages.length > 0

  // 自动滚动到聊天底部
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
    }
  }, [chatMessages, isStreaming])

  // 处理流式聊天
  const handleChatSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!chatInput.trim() || !activeTab || isStreaming) return

    const input = chatInput.trim()
    setChatInput('')
    setIsStreaming(true)

    // 如果是第一次聊天，自动收起结构化笔记以节省空间
    if (chatMessages.length === 0) {
      setIsStructuredNotesExpanded(false)
    }

    try {
      // 添加用户消息
      const userMessageId = addChatMessage(activeTab.id, {
        role: 'user',
        content: input
      })

      // 创建AI回复的临时消息
      const assistantMessageId = addChatMessage(activeTab.id, {
        role: 'assistant',
        content: ''
      })

      // 发送请求到聊天API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: input,
          context: {
            originalContent: activeTab.originalContent || '',
            aiNote: streamingNote || activeTab.aiNoteMarkdown || '',
          }
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      let fullResponse = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') continue

            try {
              const parsed = JSON.parse(data)
              if (parsed.content) {
                fullResponse += parsed.content
                updateChatMessage(activeTab.id, assistantMessageId, fullResponse)
              }
            } catch (e) {
              console.warn('解析JSON失败:', e)
            }
          }
        }
      }

      if (!fullResponse) {
        updateChatMessage(activeTab.id, assistantMessageId, '抱歉，AI暂时无法回复，请重试。')
      }
    } catch (error) {
      console.error('聊天请求失败:', error)
      addChatMessage(activeTab.id, {
        role: 'assistant',
        content: '抱歉，发生了错误，请重试。'
      })
    } finally {
      setIsStreaming(false)
    }
  }

  if (!activeTab) {
    return (
      <div className="h-full flex items-center justify-center text-gray-500 p-6">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center">
            <Brain className="w-8 h-8 text-blue-600" />
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900 mb-2">AI助手</p>
            <p className="text-sm text-gray-600">选择或创建一个标签页开始使用</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col liquid-glass">
      {/* 根据聊天状态决定布局 */}
      {!hasChatMessages ? (
        /* 无聊天时：结构化笔记占满全屏 */
        <div className="h-full flex flex-col p-6">
          {/* 简洁的标题 */}
          <div className="flex items-center space-x-3 mb-6">
            <Sparkles className="w-5 h-5 text-blue-500" />
            <h2 className="text-lg font-medium text-gray-900">结构化笔记</h2>
            {activeTab && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {streamingNote || activeTab.aiNoteMarkdown ? "已生成" : activeTab.aiAnalyzing ? "生成中" : "待分析"}
              </span>
            )}
          </div>

          {/* 结构化笔记内容区域 */}
          <div className="flex-1 overflow-y-auto">
            {!activeTab ? (
              <div className="h-full flex items-center justify-center text-gray-500">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center">
                    <Brain className="w-8 h-8 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-lg font-semibold text-gray-900 mb-2">AI助手</p>
                    <p className="text-sm text-gray-600">选择或创建一个标签页开始使用</p>
                  </div>
                </div>
              </div>
            ) : activeTab.isLoading ? (
              <div className="h-full flex items-center justify-center">
                <ModernLoader variant="dots" size="lg" text="正在分析内容..." className="text-center" />
              </div>
            ) : (streamingNote || activeTab.aiNoteMarkdown) ? (
              <div className="ai-note-content bg-gradient-to-br from-slate-50/80 to-blue-50/60 backdrop-blur-sm rounded-2xl p-8 h-full border border-slate-200/50 shadow-sm">
                <SafeMarkdown className="prose prose-lg max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-6 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
                  {streamingNote || activeTab.aiNoteMarkdown || ''}
                </SafeMarkdown>
              </div>
            ) : activeTab.aiAnalyzing ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <ModernLoader variant="dots" size="lg" text="正在生成结构化笔记..." className="text-center" />
                  <p className="text-sm text-gray-500 mt-4">AI正在为您整理知识要点</p>
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto bg-gray-100 rounded-2xl flex items-center justify-center">
                    <Brain className="w-8 h-8 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-gray-500 text-lg font-medium">准备就绪</p>
                    <p className="text-gray-400 text-sm mt-2">开始输入内容，AI将为您生成结构化笔记</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 聊天输入框 */}
          <div className="mt-6 pt-4 border-t border-gray-200/50">
            <form onSubmit={handleChatSubmit} className="flex space-x-3 bg-white/80 rounded-xl border border-gray-100/50 p-3 shadow-sm">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  placeholder="对内容有疑问？问我任何问题..."
                  disabled={isStreaming || !activeTab}
                  className="w-full px-3 py-2 border-0 bg-transparent focus:ring-0 focus:outline-none text-sm placeholder-gray-500 disabled:text-gray-400"
                />
              </div>
              <button
                type="submit"
                disabled={!chatInput.trim() || isStreaming || !activeTab}
                className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <Send size={16} />
              </button>
            </form>
          </div>
        </div>
      ) : (
        /* 有聊天时：卡片化布局 */
        <>
          {/* 结构化笔记卡片 */}
          <div className="flex-shrink-0">
            <div className="mx-4 mt-4 mb-2 bg-gradient-to-br from-white/90 to-slate-50/80 backdrop-blur-sm rounded-2xl border border-slate-200/60 shadow-lg shadow-slate-200/20">
              <div className="px-5 py-4 flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-1.5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                    <Sparkles className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="font-semibold text-slate-800 text-sm">结构化笔记</h3>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 border border-blue-200/50">
                    {streamingNote || activeTab?.aiNoteMarkdown ? "✨ 已生成" : activeTab?.aiAnalyzing ? "🔄 生成中" : "⏳ 待分析"}
                  </span>
                </div>
                <button
                  onClick={() => setIsStructuredNotesExpanded(!isStructuredNotesExpanded)}
                  className="p-1.5 hover:bg-blue-50 rounded-lg transition-colors duration-200 group"
                >
                  {isStructuredNotesExpanded ? (
                    <ChevronUp className="w-4 h-4 text-blue-600 group-hover:text-blue-700" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-blue-600 group-hover:text-blue-700" />
                  )}
                </button>
              </div>

              <div className={`transition-all duration-300 ease-in-out overflow-hidden ${isStructuredNotesExpanded ? 'max-h-[70vh] opacity-100' : 'max-h-[30vh] opacity-100'}`}>
                <div className="overflow-y-auto px-4 pb-4" style={{ maxHeight: isStructuredNotesExpanded ? '70vh' : '30vh' }}>
                  {activeTab?.isLoading ? (
                    <div className="py-8">
                      <ModernLoader variant="dots" size="md" text="正在分析..." className="text-center" />
                    </div>
                  ) : (streamingNote || activeTab?.aiNoteMarkdown) ? (
                    <div className="ai-note-content bg-gradient-to-br from-slate-50/50 to-blue-50/30 rounded-xl p-4">
                      <SafeMarkdown className={`prose max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:rounded-r-lg ${isStructuredNotesExpanded ? 'prose-sm' : 'prose-xs'}`}>
                        {isStructuredNotesExpanded ?
                          (streamingNote || activeTab?.aiNoteMarkdown || '') :
                          // 预览模式：显示前200个字符
                          ((streamingNote || activeTab?.aiNoteMarkdown || '').substring(0, 200) + (((streamingNote || activeTab?.aiNoteMarkdown || '').length > 200) ? '...' : ''))
                        }
                      </SafeMarkdown>
                      {!isStructuredNotesExpanded && (streamingNote || activeTab?.aiNoteMarkdown || '').length > 200 && (
                        <div className="mt-2 text-center">
                          <button
                            onClick={() => setIsStructuredNotesExpanded(true)}
                            className="text-xs text-blue-600 hover:text-blue-700 font-medium"
                          >
                            点击展开查看完整内容
                          </button>
                        </div>
                      )}
                    </div>
                  ) : activeTab?.aiAnalyzing ? (
                    <div className="py-8">
                      <ModernLoader variant="dots" size="md" text="正在生成结构化笔记..." className="text-center" />
                    </div>
                  ) : (
                    <div className="py-8 text-center">
                      <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-3">
                        <Brain className="w-6 h-6 text-gray-400" />
                      </div>
                      <p className="text-gray-500 text-sm">暂无结构化笔记</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 聊天区域 */}
          <div className="flex-1 flex flex-col min-h-0">
            <div ref={chatContainerRef} className="flex-1 overflow-y-auto p-4 space-y-4 bg-white/50" style={{ minHeight: '300px' }}>
              {chatMessages.map((message) => {
                if (!message.id || !message.content) return null
                return (
                  <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[85%] px-4 py-3 rounded-2xl text-sm shadow-sm ${
                      message.role === 'user'
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                        : 'bg-white border border-gray-200 text-gray-900'
                    }`}>
                      {message.role === 'assistant' ? (
                        <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-gray-800 prose-p:text-gray-700 prose-ul:text-gray-700 prose-ol:text-gray-700 prose-code:text-blue-600 prose-code:bg-blue-50">
                          {message.content || '正在思考...'}
                        </SafeMarkdown>
                      ) : (
                        <span>{message.content}</span>
                      )}
                    </div>
                  </div>
                )
              }).filter(Boolean)}

              {isStreaming && (
                <div className="flex justify-start">
                  <div className="bg-white border border-gray-200 rounded-2xl px-4 py-3 shadow-sm">
                    <div className="flex items-center space-x-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      </div>
                      <span className="text-sm text-gray-500">AI正在思考...</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex-shrink-0 p-4 mx-4 mb-4">
              <form onSubmit={handleChatSubmit} className="flex space-x-3 bg-white/80 rounded-xl border border-gray-100/50 p-3 shadow-sm">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    placeholder="输入消息..."
                    disabled={isStreaming}
                    className="w-full px-3 py-2 border-0 bg-transparent focus:ring-0 focus:outline-none text-sm placeholder-gray-500 disabled:text-gray-400"
                  />
                </div>
                <button
                  type="submit"
                  disabled={!chatInput.trim() || isStreaming}
                  className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <Send size={16} />
                </button>
              </form>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default AIAssistant
